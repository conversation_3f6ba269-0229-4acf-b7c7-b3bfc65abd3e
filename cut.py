#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Colon Cancer Dataset Splitting Script
Split data in colon_subset into 80% training set and 20% test set
"""

import os
import shutil
import random
from pathlib import Path

def create_directories(base_path):
    """Create directory structure for training and test sets"""
    directories = [
        'train/colon_aca',
        'train/colon_n',
        'test/colon_aca',
        'test/colon_n'
    ]

    for directory in directories:
        dir_path = Path(base_path) / directory
        dir_path.mkdir(parents=True, exist_ok=True)
        print(f"Created directory: {dir_path}")

def split_dataset(source_dir, dest_dir, train_ratio=0.8):
    """
    Split dataset into training and test sets according to specified ratio

    Args:
        source_dir: Source data directory
        dest_dir: Destination directory
        train_ratio: Training set ratio, default 0.8 (80%)
    """

    # Set random seed to ensure reproducible results
    random.seed(42)

    # Create destination directory structure
    create_directories(dest_dir)

    # Process data for both categories
    categories = ['colon_aca', 'colon_n']

    for category in categories:
        source_category_path = Path(source_dir) / category

        # Get all image files in this category
        image_files = list(source_category_path.glob('*.jpg'))

        print(f"\nProcessing category: {category}")
        print(f"Total files: {len(image_files)}")

        # Randomly shuffle file list
        random.shuffle(image_files)

        # Calculate number of files for training and test sets
        train_count = int(len(image_files) * train_ratio)
        test_count = len(image_files) - train_count

        print(f"Training set files: {train_count}")
        print(f"Test set files: {test_count}")

        # Split into training and test sets
        train_files = image_files[:train_count]
        test_files = image_files[train_count:]

        # Copy training set files
        train_dest_path = Path(dest_dir) / 'train' / category
        for file_path in train_files:
            dest_file_path = train_dest_path / file_path.name
            shutil.copy2(file_path, dest_file_path)

        # Copy test set files
        test_dest_path = Path(dest_dir) / 'test' / category
        for file_path in test_files:
            dest_file_path = test_dest_path / file_path.name
            shutil.copy2(file_path, dest_file_path)

        print(f"Completed data splitting for {category} category")

def verify_split(dest_dir):
    """Verify data splitting results"""
    print("\n=== Data Splitting Results Verification ===")

    categories = ['colon_aca', 'colon_n']
    splits = ['train', 'test']

    total_train = 0
    total_test = 0

    for split in splits:
        for category in categories:
            path = Path(dest_dir) / split / category
            count = len(list(path.glob('*.jpg')))
            print(f"{split}/{category}: {count} images")

            if split == 'train':
                total_train += count
            else:
                total_test += count

    print(f"\nTotal:")
    print(f"Training set total: {total_train}")
    print(f"Test set total: {total_test}")
    print(f"Total files: {total_train + total_test}")
    print(f"Training set ratio: {total_train / (total_train + total_test) * 100:.1f}%")
    print(f"Test set ratio: {total_test / (total_train + total_test) * 100:.1f}%")

def main():
    """Main function"""
    # Set paths
    source_dir = "colon_subset"
    dest_dir = "colon_dataset_split"

    print("Starting colon cancer dataset splitting...")
    print(f"Source directory: {source_dir}")
    print(f"Destination directory: {dest_dir}")

    # Check if source directory exists
    if not Path(source_dir).exists():
        print(f"Error: Source directory {source_dir} does not exist!")
        return

    # Execute data splitting
    split_dataset(source_dir, dest_dir, train_ratio=0.8)

    # Verify results
    verify_split(dest_dir)

    print("\nDataset splitting completed!")

if __name__ == "__main__":
    main()
