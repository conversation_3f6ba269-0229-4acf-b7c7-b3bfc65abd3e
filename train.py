#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Colon Cancer Cell Classifier
Deep learning-based classification of colon cancer cell images (PyTorch version)
"""

import os
import sys
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import confusion_matrix, roc_curve, auc, precision_recall_curve
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from torchvision import transforms, models
from torchvision.datasets import ImageFolder
import pickle
import warnings
from PIL import Image
warnings.filterwarnings('ignore')

# Try to import SHAP, set flag if failed
try:
    import shap
    SHAP_AVAILABLE = True
except ImportError as e:
    print(f"Warning: SHAP library import failed ({e}), will skip SHAP analysis")
    SHAP_AVAILABLE = False

# Set font support for plots
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False

# Ensure UTF-8 output encoding
if sys.stdout.encoding != 'utf-8':
    sys.stdout.reconfigure(encoding='utf-8')

class ColonCancerModel(nn.Module):
    """PyTorch Colon Cancer Cell Classification Model"""

    def __init__(self, num_classes=1):
        super(ColonCancerModel, self).__init__()
        # Use pre-trained VGG16 as backbone model
        self.backbone = models.vgg16(pretrained=True)

        # Freeze feature extraction layer weights
        for param in self.backbone.features.parameters():
            param.requires_grad = False

        # Get VGG16 feature extractor output dimensions
        # VGG16 features output is [batch_size, 512, 7, 7] (for 224x224 input)

        # Replace classifier
        self.backbone.classifier = nn.Sequential(
            nn.Dropout(0.5),
            nn.Linear(512 * 7 * 7, 4096),
            nn.ReLU(True),
            nn.Dropout(0.5),
            nn.Linear(4096, 128),
            nn.ReLU(True),
            nn.Dropout(0.3),
            nn.Linear(128, 64),
            nn.ReLU(True),
            nn.Dropout(0.2),
            nn.Linear(64, num_classes)
        )

    def forward(self, x):
        # Through feature extractor
        x = self.backbone.features(x)
        # Global average pooling
        x = self.backbone.avgpool(x)
        # Flatten
        x = torch.flatten(x, 1)
        # Through classifier
        x = self.backbone.classifier(x)
        return x

def set_random_seeds(seed=42):
    """Set all random seeds to ensure reproducibility"""
    import random
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    # Ensure CUDA operation determinism
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False
    print(f"Random seed set: {seed}")

class ColonCancerClassifier:
    """Colon Cancer Cell Classifier Class"""

    def __init__(self, img_size=(224, 224), batch_size=32, random_seed=42):
        """
        Initialize classifier

        Args:
            img_size: Image size
            batch_size: Batch size
            random_seed: Random seed
        """
        # Set random seed
        set_random_seeds(random_seed)

        self.img_size = img_size
        self.batch_size = batch_size
        self.random_seed = random_seed
        self.model = None
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.history = {'train_loss': [], 'train_acc': [], 'val_loss': [], 'val_acc': []}
        self.class_names = ['colon_aca', 'colon_n']  # Cancer cells, Normal cells
        print(f"Using device: {self.device}")
        print(f"Random seed: {random_seed}")

    def create_model(self):
        """Create CNN model"""
        self.model = ColonCancerModel(num_classes=1)
        self.model.to(self.device)
        return self.model

    def get_transforms(self, is_training=True):
        """Get data transforms"""
        # Use same transforms for training and testing to ensure fair comparison
        return transforms.Compose([
            transforms.Resize((self.img_size[0], self.img_size[1])),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])

    def prepare_data(self, data_dir='colon_dataset_split', random_seed=None, include_validation=True):
        """
        Prepare training, validation, and testing data

        IMPORTANT: Test set is kept completely isolated and only used for final evaluation
        to prevent data leakage. Validation set is created from training data for model selection.

        Args:
            data_dir: Dataset directory
            random_seed: Random seed, if None use instance random seed
            include_validation: Whether to create validation split from training data (default: True)
        """
        if random_seed is None:
            random_seed = self.random_seed

        train_dir = os.path.join(data_dir, 'train')
        test_dir = os.path.join(data_dir, 'test')

        # Create datasets
        train_dataset = ImageFolder(train_dir, transform=self.get_transforms(is_training=True))
        test_dataset = ImageFolder(test_dir, transform=self.get_transforms(is_training=False))

        if include_validation:
            # Split training data into train and validation (80/20 split)
            # This ensures test set remains completely isolated
            from torch.utils.data import random_split
            train_size = int(0.8 * len(train_dataset))
            val_size = len(train_dataset) - train_size

            # Set generator for reproducible split
            generator = torch.Generator().manual_seed(random_seed)
            train_subset, val_subset = random_split(train_dataset, [train_size, val_size], generator=generator)

            print(f"Data split information (with validation):")
            print(f"  Training set: {len(train_subset)} samples")
            print(f"  Validation set: {len(val_subset)} samples")
            print(f"  Test set: {len(test_dataset)} samples (COMPLETELY ISOLATED - only for final evaluation)")

            # Create data loaders
            train_loader = DataLoader(train_subset, batch_size=self.batch_size, shuffle=True, num_workers=4)
            val_loader = DataLoader(val_subset, batch_size=self.batch_size, shuffle=False, num_workers=4)
            test_loader = DataLoader(test_dataset, batch_size=self.batch_size, shuffle=False, num_workers=4)

            return train_loader, val_loader, test_loader
        else:
            print(f"Data split information:")
            print(f"  Training set: {len(train_dataset)} samples (complete training set)")
            print(f"  Test set: {len(test_dataset)} samples (ISOLATED - only for final evaluation)")

            # Create data loaders
            train_loader = DataLoader(train_dataset, batch_size=self.batch_size, shuffle=True, num_workers=4)
            test_loader = DataLoader(test_dataset, batch_size=self.batch_size, shuffle=False, num_workers=4)

            return train_loader, test_loader

    def train_epoch(self, train_loader, criterion, optimizer):
        """Train one epoch"""
        self.model.train()
        running_loss = 0.0
        correct = 0
        total = 0

        for batch_idx, (data, target) in enumerate(train_loader):
            data, target = data.to(self.device), target.to(self.device).float()

            optimizer.zero_grad()
            output = self.model(data).squeeze()
            loss = criterion(output, target)
            loss.backward()
            optimizer.step()

            running_loss += loss.item()
            predicted = (torch.sigmoid(output) > 0.5).float()
            total += target.size(0)
            correct += (predicted == target).sum().item()

            if batch_idx % 10 == 0:
                print(f'Batch {batch_idx}/{len(train_loader)}, Loss: {loss.item():.4f}')

        epoch_loss = running_loss / len(train_loader)
        epoch_acc = correct / total
        return epoch_loss, epoch_acc

    def validate_epoch(self, val_loader, criterion):
        """Validate one epoch"""
        self.model.eval()
        running_loss = 0.0
        correct = 0
        total = 0

        with torch.no_grad():
            for data, target in val_loader:
                data, target = data.to(self.device), target.to(self.device).float()
                output = self.model(data).squeeze()
                loss = criterion(output, target)

                running_loss += loss.item()
                predicted = (torch.sigmoid(output) > 0.5).float()
                total += target.size(0)
                correct += (predicted == target).sum().item()

        epoch_loss = running_loss / len(val_loader)
        epoch_acc = correct / total
        return epoch_loss, epoch_acc

    def train(self, epochs=50, data_dir='colon_dataset_split'):
        """
        Train model with proper data isolation to prevent data leakage

        IMPORTANT: Test set is kept completely isolated and only used for final evaluation.
        Validation set is used for model selection and early stopping.

        Args:
            epochs: Number of training epochs
            data_dir: Dataset directory
        """
        print("Starting data preparation...")
        # Always use validation split to prevent data leakage
        train_loader, val_loader, test_loader = self.prepare_data(data_dir, include_validation=True)

        print("Creating model...")
        self.create_model()

        print("Model structure:")
        print(self.model)

        # Set loss function and optimizer
        criterion = nn.BCEWithLogitsLoss()
        optimizer = optim.Adam(self.model.parameters(), lr=0.0001)
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', factor=0.5, patience=5, min_lr=1e-7)

        # Early stopping parameters - based on validation loss (NO DATA LEAKAGE)
        best_val_loss = float('inf')
        patience = 10
        patience_counter = 0

        print("Starting training...")
        print("NOTE: Test set is completely isolated and will only be used for final evaluation")

        for epoch in range(epochs):
            print(f'\nEpoch {epoch+1}/{epochs}')
            print('-' * 50)

            # Training
            train_loss, train_acc = self.train_epoch(train_loader, criterion, optimizer)

            # Validation (NO TEST SET USED DURING TRAINING - prevents data leakage)
            val_loss, val_acc = self.validate_epoch(val_loader, criterion)

            # Learning rate scheduling - based on validation loss
            scheduler.step(val_loss)

            # Record history (only train and validation, NO test data during training)
            self.history['train_loss'].append(train_loss)
            self.history['train_acc'].append(train_acc)
            self.history['val_loss'].append(val_loss)
            self.history['val_acc'].append(val_acc)

            print(f'Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.4f}')
            print(f'Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.4f}')

            # Save best model - based on validation loss (NO DATA LEAKAGE)
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                patience_counter = 0
                torch.save(self.model.state_dict(), 'best_colon_cancer_model.pth')
                print('Saved best model (based on validation loss)')
            else:
                patience_counter += 1

            # Early stopping - based on validation loss (NO DATA LEAKAGE)
            if patience_counter >= patience:
                print(f'Early stopping at epoch {epoch+1} (based on validation loss)')
                break

        # Load best model
        self.model.load_state_dict(torch.load('best_colon_cancer_model.pth'))

        # Save final model
        torch.save(self.model.state_dict(), 'colon_cancer_classifier.pth')
        print("Model saved as colon_cancer_classifier.pth")

        # Save training history
        with open('training_history.pkl', 'wb') as f:
            pickle.dump(self.history, f)

        # Plot training history (only train/validation, no test data)
        self.plot_training_history()

        # FINAL EVALUATION: Test set used ONLY ONCE for final evaluation
        print("\n" + "="*60)
        print("FINAL EVALUATION: Using test set for the FIRST AND ONLY TIME")
        print("="*60)
        test_loss, test_accuracy = self.validate_epoch(test_loader, criterion)
        print(f"Final test set loss: {test_loss:.4f}")
        print(f"Final test set accuracy: {test_accuracy:.4f}")

        # Perform comprehensive test set analysis (ONLY ONCE)
        print("\nStarting comprehensive model analysis on test set...")
        self.comprehensive_analysis(test_loader)

        return self.history
    
    def plot_training_history(self):
        """Plot training history (train/validation only - no test data leakage)"""
        if not self.history or not self.history['train_loss']:
            print("No training history to plot")
            return

        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

        # Plot accuracy comparison (train vs validation)
        ax1.plot(self.history['train_acc'], label='Training Accuracy', color='blue', linewidth=2)
        if 'val_acc' in self.history and self.history['val_acc']:
            ax1.plot(self.history['val_acc'], label='Validation Accuracy', color='green', linewidth=2)
        ax1.set_title('Model Accuracy: Training vs Validation', fontsize=14, fontweight='bold')
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('Accuracy')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        ax1.set_ylim(0, 1)

        # Plot loss comparison (train vs validation)
        ax2.plot(self.history['train_loss'], label='Training Loss', color='red', linewidth=2)
        if 'val_loss' in self.history and self.history['val_loss']:
            ax2.plot(self.history['val_loss'], label='Validation Loss', color='orange', linewidth=2)
        ax2.set_title('Model Loss: Training vs Validation', fontsize=14, fontweight='bold')
        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('Loss')
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig('training_history.png', dpi=300, bbox_inches='tight')
        plt.show()
        print("Training history plot saved as training_history.png")
        print("NOTE: Only training and validation data shown - test set kept isolated")

    def load_model(self, model_path):
        """
        Load trained model

        Args:
            model_path: Model file path
        """
        if os.path.exists(model_path):
            if self.model is None:
                self.create_model()
            self.model.load_state_dict(torch.load(model_path, map_location=self.device))
            self.model.eval()
            print(f"Model loaded from {model_path}")
            return True
        else:
            print(f"Model file {model_path} does not exist")
            return False

    def predict_image(self, image_path):
        """
        Predict single image

        Args:
            image_path: Image file path
        """
        if self.model is None:
            print("Please load model first")
            return None

        # Read and preprocess image
        img = Image.open(image_path).convert('RGB')
        transform = self.get_transforms(is_training=False)
        img_tensor = transform(img).unsqueeze(0).to(self.device)

        # Predict
        self.model.eval()
        with torch.no_grad():
            output = self.model(img_tensor)
            prediction = torch.sigmoid(output).item()

        # Interpret results
        if prediction > 0.5:
            result = "Normal Cell"
            confidence = prediction
        else:
            result = "Cancer Cell"
            confidence = 1 - prediction

        return {
            'prediction': result,
            'confidence': confidence,
            'raw_score': prediction
        }
    
    def load_data_split(self, split_file='data_split.pkl'):
        """Load data split information"""
        if os.path.exists(split_file):
            with open(split_file, 'rb') as f:
                self.data_split = pickle.load(f)
            return True
        return False

    def comprehensive_analysis(self, test_loader=None):
        """
        Perform comprehensive model analysis including confusion matrix, SHAP analysis and various visualizations

        IMPORTANT: This function should ONLY be called for final evaluation with the test set.
        It should NOT be called during training to avoid data leakage.

        Args:
            test_loader: Test data loader, if None will create automatically
        """
        if self.model is None:
            print("❌ No available model for analysis")
            return

        print("Performing comprehensive model analysis...")
        print("⚠️  WARNING: This analysis uses the test set and should only be done ONCE for final evaluation")

        # If no test loader provided, create one
        if test_loader is None:
            print("Creating test data loader for final evaluation...")
            # Use validation=False to get the isolated test set
            _, test_loader = self.prepare_data(include_validation=False)

        # Get prediction results on test data
        self.model.eval()
        y_pred_proba = []
        y_true = []

        with torch.no_grad():
            for data, target in test_loader:
                data = data.to(self.device)
                output = self.model(data)
                proba = torch.sigmoid(output).cpu().numpy()
                y_pred_proba.extend(proba.flatten())
                y_true.extend(target.numpy())

        y_pred_proba = np.array(y_pred_proba)
        y_true = np.array(y_true)
        y_pred = (y_pred_proba > 0.5).astype(int)

        # Get class names
        class_names = self.class_names

        print(f"📊 Test set statistics:")
        print(f"  Total samples: {len(y_true)}")
        print(f"  Classes: {class_names}")
        for i, name in enumerate(class_names):
            count = np.sum(y_true == i)
            print(f"  {name}: {count} samples")

        # 1. Confusion matrix analysis
        print("\n1️⃣ Generating confusion matrix...")
        self.plot_confusion_matrix(y_true, y_pred, class_names)

        # 2. ROC curve and AUC analysis
        print("2️⃣ Generating ROC curve...")
        self.plot_roc_curve(y_true, y_pred_proba)

        # 3. Precision-recall curve
        print("3️⃣ Generating precision-recall curve...")
        self.plot_precision_recall_curve(y_true, y_pred_proba)

        # 4. Classification report visualization
        print("4️⃣ Generating classification report...")
        self.plot_classification_report(y_true, y_pred, class_names)

        # 5. Prediction probability distribution
        print("5️⃣ Generating prediction probability distribution...")
        self.plot_prediction_distribution(y_true, y_pred_proba, class_names)

        # 6. Feature importance analysis (SHAP or alternative method)
        print("6️⃣ Performing feature importance analysis...")
        self.shap_analysis(test_loader)

        # 7. Model performance metrics summary
        print("7️⃣ Generating performance metrics summary...")
        self.plot_performance_metrics(y_true, y_pred, y_pred_proba)

        # 8. Error analysis
        print("8️⃣ Performing error analysis...")
        self.error_analysis(test_loader, y_true, y_pred, y_pred_proba)

        print("\n✅ Comprehensive analysis complete! All charts saved to current directory.")

    def plot_confusion_matrix(self, y_true, y_pred, class_names):
        """Plot confusion matrix"""
        cm = confusion_matrix(y_true, y_pred)

        plt.figure(figsize=(10, 8))
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                   xticklabels=class_names, yticklabels=class_names,
                   cbar_kws={'label': 'Sample Count'})
        plt.title('Confusion Matrix', fontsize=16, fontweight='bold')
        plt.xlabel('Predicted Label', fontsize=12)
        plt.ylabel('True Label', fontsize=12)

        # Add accuracy information
        accuracy = np.trace(cm) / np.sum(cm)
        plt.text(0.5, -0.1, f'Overall Accuracy: {accuracy:.4f}',
                transform=plt.gca().transAxes, ha='center', fontsize=12)

        plt.tight_layout()
        plt.savefig('confusion_matrix.png', dpi=300, bbox_inches='tight')
        plt.show()
        print("Confusion matrix saved as confusion_matrix.png")

    def plot_roc_curve(self, y_true, y_pred_proba):
        """Plot ROC curve"""
        fpr, tpr, _ = roc_curve(y_true, y_pred_proba)
        roc_auc = auc(fpr, tpr)

        plt.figure(figsize=(8, 6))
        plt.plot(fpr, tpr, color='darkorange', lw=2,
                label=f'ROC Curve (AUC = {roc_auc:.4f})')
        plt.plot([0, 1], [0, 1], color='navy', lw=2, linestyle='--',
                label='Random Classifier')
        plt.xlim([0.0, 1.0])
        plt.ylim([0.0, 1.05])
        plt.xlabel('False Positive Rate (FPR)', fontsize=12)
        plt.ylabel('True Positive Rate (TPR)', fontsize=12)
        plt.title('ROC Curve', fontsize=16, fontweight='bold')
        plt.legend(loc="lower right")
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.savefig('roc_curve.png', dpi=300, bbox_inches='tight')
        plt.show()
        print("ROC curve saved as roc_curve.png")

    def plot_precision_recall_curve(self, y_true, y_pred_proba):
        """Plot precision-recall curve"""
        precision, recall, _ = precision_recall_curve(y_true, y_pred_proba)
        pr_auc = auc(recall, precision)

        plt.figure(figsize=(8, 6))
        plt.plot(recall, precision, color='blue', lw=2,
                label=f'PR Curve (AUC = {pr_auc:.4f})')
        plt.xlabel('Recall', fontsize=12)
        plt.ylabel('Precision', fontsize=12)
        plt.title('Precision-Recall Curve', fontsize=16, fontweight='bold')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.savefig('precision_recall_curve.png', dpi=300, bbox_inches='tight')
        plt.show()
        print("Precision-recall curve saved as precision_recall_curve.png")

    def plot_classification_report(self, y_true, y_pred, class_names):
        """Visualize classification report"""
        from sklearn.metrics import precision_score, recall_score, f1_score

        # Calculate metrics
        precision = precision_score(y_true, y_pred, average=None)
        recall = recall_score(y_true, y_pred, average=None)
        f1 = f1_score(y_true, y_pred, average=None)

        # Create dataframe
        metrics_df = pd.DataFrame({
            'Precision': precision,
            'Recall': recall,
            'F1-Score': f1
        }, index=class_names)

        # Plot bar chart
        fig, ax = plt.subplots(figsize=(10, 6))
        metrics_df.plot(kind='bar', ax=ax, width=0.8)
        plt.title('Classification Performance Metrics', fontsize=16, fontweight='bold')
        plt.xlabel('Class', fontsize=12)
        plt.ylabel('Score', fontsize=12)
        plt.legend(title='Metrics')
        plt.xticks(rotation=45)
        plt.ylim(0, 1.1)

        # Add value labels
        for i, (idx, row) in enumerate(metrics_df.iterrows()):
            for j, value in enumerate(row):
                ax.text(i + (j-1)*0.25, value + 0.02, f'{value:.3f}',
                       ha='center', va='bottom', fontsize=10)

        plt.tight_layout()
        plt.savefig('classification_report.png', dpi=300, bbox_inches='tight')
        plt.show()
        print("Classification report saved as classification_report.png")

    def plot_prediction_distribution(self, y_true, y_pred_proba, class_names):
        """Plot prediction probability distribution"""
        plt.figure(figsize=(12, 5))

        # Plot prediction probability distribution for each class separately
        plt.subplot(1, 2, 1)
        for i, class_name in enumerate(class_names):
            mask = y_true == i
            plt.hist(y_pred_proba[mask], bins=30, alpha=0.7,
                    label=f'True Class: {class_name}', density=True)
        plt.xlabel('Prediction Probability', fontsize=12)
        plt.ylabel('Density', fontsize=12)
        plt.title('Prediction Probability Distribution', fontsize=14, fontweight='bold')
        plt.legend()
        plt.grid(True, alpha=0.3)

        # Plot box plot of prediction probabilities
        plt.subplot(1, 2, 2)
        data_for_box = [y_pred_proba[y_true == i] for i in range(len(class_names))]
        plt.boxplot(data_for_box, labels=class_names)
        plt.ylabel('Prediction Probability', fontsize=12)
        plt.title('Prediction Probability Box Plot', fontsize=14, fontweight='bold')
        plt.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig('prediction_distribution.png', dpi=300, bbox_inches='tight')
        plt.show()
        print("Prediction probability distribution saved as prediction_distribution.png")

    def shap_analysis(self, test_loader):
        """
        SHAP analysis - Model interpretability analysis

        IMPORTANT: Uses training data as background to avoid data leakage.
        Previously used test data as background which caused data leakage.
        """
        if not SHAP_AVAILABLE:
            print("SHAP library not available, skipping SHAP analysis")
            # Create alternative feature importance analysis
            self.alternative_feature_analysis()
            return

        try:
            print("Performing SHAP analysis...")

            # FIXED: Get training data for SHAP background (avoid data leakage)
            # Previously used test data as background which caused data leakage
            print("Loading training data for SHAP background...")
            train_loader, _ = self.prepare_data()

            # Get a small batch of training data as background for SHAP explainer
            train_batch_data, _ = next(iter(train_loader))
            train_batch_data = train_batch_data.to(self.device)

            # Limit background sample size to speed up computation
            background_size = min(10, len(train_batch_data))
            background_data = train_batch_data[:background_size]

            # Create SHAP explainer using training data as background (no data leakage)
            explainer = shap.GradientExplainer(self.model, background_data)

            # Get test data label information, select samples from different classes
            all_data = []
            all_labels = []
            all_shap = []

            # Collect test data and calculate SHAP values (using training data as background)
            print("Calculating SHAP values for test samples...")
            for i, (data, target) in enumerate(test_loader):
                if len(all_data) >= 20:  # Collect enough samples
                    break
                data = data.to(self.device)

                # Calculate SHAP values for test data using training background
                batch_shap = explainer.shap_values(data)
                if isinstance(batch_shap, list):
                    batch_shap = batch_shap[0]

                all_data.extend(data.cpu())
                all_labels.extend(target.numpy())
                all_shap.extend(batch_shap)

            # Plot SHAP summary
            plt.figure(figsize=(12, 8))

            # Select samples from each class
            class_0_indices = [i for i, label in enumerate(all_labels) if label == 0][:2]  # Cancer cells
            class_1_indices = [i for i, label in enumerate(all_labels) if label == 1][:2]  # Normal cells

            selected_indices = class_0_indices + class_1_indices
            class_names_display = ['colon_aca', 'colon_aca', 'colon_n', 'colon_n']

            # Create 2x3 grid layout: 2 rows, 3 columns (original image, cancer SHAP, normal SHAP)
            fig, axes = plt.subplots(2, 3, figsize=(15, 10))

            for idx, sample_idx in enumerate(selected_indices):
                row = idx
                if row >= 2:  # Only show first 2 samples
                    break

                # First column: Original image
                original_img = all_data[sample_idx].numpy().transpose(1, 2, 0)
                # Denormalize
                mean = np.array([0.485, 0.456, 0.406])
                std = np.array([0.229, 0.224, 0.225])
                original_img = original_img * std + mean
                original_img = np.clip(original_img, 0, 1)

                axes[row, 0].imshow(original_img)
                axes[row, 0].set_title(class_names_display[idx], fontsize=14, fontweight='bold')
                axes[row, 0].axis('off')

                # Second column: SHAP heatmap (for cancer class)
                sample_shap = all_shap[sample_idx]  # Shape: (3, 224, 224)
                shap_combined = np.mean(sample_shap, axis=0)

                im1 = axes[row, 1].imshow(shap_combined, cmap='RdBu_r', alpha=0.8)
                axes[row, 1].set_title('SHAP (Cancer)', fontsize=12, fontweight='bold')
                axes[row, 1].axis('off')

                # Third column: SHAP heatmap (for normal class)
                # For binary classification, normal cell SHAP values are opposite of cancer
                shap_normal = -shap_combined

                im2 = axes[row, 2].imshow(shap_normal, cmap='RdBu_r', alpha=0.8)
                axes[row, 2].set_title('SHAP (Normal)', fontsize=12, fontweight='bold')
                axes[row, 2].axis('off')

            # Add color bars
            fig.colorbar(im1, ax=axes[:, 1], shrink=0.6, aspect=20, label='SHAP Values (Cancer)')
            fig.colorbar(im2, ax=axes[:, 2], shrink=0.6, aspect=20, label='SHAP Values (Normal)')

            # Add main title
            plt.suptitle('SHAP Interpretability Analysis - Colon Cancer Cell Classification\n(Red: Positive influence, Blue: Negative influence)',
                        fontsize=16, fontweight='bold', y=0.95)

            plt.tight_layout()
            plt.subplots_adjust(top=0.88)
            plt.savefig('shap_analysis.png', dpi=300, bbox_inches='tight')
            plt.show()
            print("SHAP analysis saved as shap_analysis.png")

        except Exception as e:
            print(f"SHAP analysis error: {e}")
            print("Skipping SHAP analysis, using alternative method...")
            self.alternative_feature_analysis()

    def alternative_feature_analysis(self):
        """Alternative feature importance analysis (when SHAP is not available)"""
        print("Performing alternative feature importance analysis...")

        # Use gradient analysis to evaluate feature importance
        try:
            # Create a simple feature importance visualization
            plt.figure(figsize=(12, 8))

            # Simulate feature importance (based on model layer weights)
            if hasattr(self.model, 'layers'):
                # Get weights from last convolutional layer
                conv_layers = [layer for layer in self.model.layers if 'conv' in layer.name.lower()]
                if conv_layers:
                    last_conv = conv_layers[-1]
                    if hasattr(last_conv, 'get_weights') and last_conv.get_weights():
                        weights = last_conv.get_weights()[0]
                        # Calculate mean absolute value of weights as importance metric
                        importance = np.mean(np.abs(weights), axis=(0, 1, 2))

                        plt.bar(range(len(importance)), importance)
                        plt.xlabel('Feature Channel', fontsize=12)
                        plt.ylabel('Importance Score', fontsize=12)
                        plt.title('Feature Importance Analysis (Weight-based)', fontsize=16, fontweight='bold')
                        plt.grid(True, alpha=0.3)
                        plt.tight_layout()
                        plt.savefig('feature_importance.png', dpi=300, bbox_inches='tight')
                        plt.show()
                        print("Feature importance analysis saved as feature_importance.png")
                        return

            # If unable to get weights, create an explanation plot
            plt.text(0.5, 0.5, 'Feature Importance Analysis\n(SHAP not available)\n\nRecommend installing compatible SHAP library\nfor more detailed interpretability analysis',
                    ha='center', va='center', fontsize=14,
                    bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue"))
            plt.xlim(0, 1)
            plt.ylim(0, 1)
            plt.axis('off')
            plt.title('Feature Importance Analysis', fontsize=16, fontweight='bold')
            plt.tight_layout()
            plt.savefig('feature_importance.png', dpi=300, bbox_inches='tight')
            plt.show()
            print("Feature importance explanation saved as feature_importance.png")

        except Exception as e:
            print(f"Alternative feature analysis also failed: {e}")
            print("Skipping feature importance analysis")

    def plot_performance_metrics(self, y_true, y_pred, y_pred_proba):
        """Plot model performance metrics summary"""
        from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score

        # Calculate metrics
        accuracy = accuracy_score(y_true, y_pred)
        precision = precision_score(y_true, y_pred, average='weighted')
        recall = recall_score(y_true, y_pred, average='weighted')
        f1 = f1_score(y_true, y_pred, average='weighted')

        # ROC AUC
        fpr, tpr, _ = roc_curve(y_true, y_pred_proba)
        roc_auc = auc(fpr, tpr)

        # Create performance metrics plot
        metrics = ['Accuracy', 'Precision', 'Recall', 'F1-Score', 'ROC AUC']
        values = [accuracy, precision, recall, f1, roc_auc]

        plt.figure(figsize=(10, 6))
        bars = plt.bar(metrics, values, color=['skyblue', 'lightgreen', 'lightcoral', 'gold', 'plum'])
        plt.ylim(0, 1.1)
        plt.title('Model Performance Metrics Summary', fontsize=16, fontweight='bold')
        plt.ylabel('Score', fontsize=12)

        # Add value labels
        for bar, value in zip(bars, values):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.02,
                    f'{value:.4f}', ha='center', va='bottom', fontsize=12, fontweight='bold')

        plt.xticks(rotation=45)
        plt.grid(True, alpha=0.3, axis='y')
        plt.tight_layout()
        plt.savefig('performance_metrics.png', dpi=300, bbox_inches='tight')
        plt.show()
        print("Performance metrics summary saved as performance_metrics.png")

    def error_analysis(self, test_loader, y_true, y_pred, y_pred_proba):
        """Error analysis - Analyze model prediction errors"""
        # Find incorrectly predicted samples
        wrong_predictions = y_true != y_pred
        wrong_indices = np.where(wrong_predictions)[0]

        if len(wrong_indices) == 0:
            print("Model has no prediction errors on test set!")
            return

        print(f"Found {len(wrong_indices)} incorrectly predicted samples")

        # Analyze error types
        false_positives = np.sum((y_true == 0) & (y_pred == 1))
        false_negatives = np.sum((y_true == 1) & (y_pred == 0))

        plt.figure(figsize=(12, 5))

        # Error type distribution
        plt.subplot(1, 2, 1)
        error_types = ['False Positive\n(Misdiagnosed as Cancer)', 'False Negative\n(Missed Cancer)']
        error_counts = [false_positives, false_negatives]
        colors = ['lightcoral', 'lightsalmon']

        bars = plt.bar(error_types, error_counts, color=colors)
        plt.title('Prediction Error Type Analysis', fontsize=14, fontweight='bold')
        plt.ylabel('Error Count', fontsize=12)

        # Add value labels
        for bar, count in zip(bars, error_counts):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5,
                    str(count), ha='center', va='bottom', fontsize=12, fontweight='bold')

        # Confidence distribution of error samples
        plt.subplot(1, 2, 2)
        wrong_confidences = y_pred_proba[wrong_predictions]
        plt.hist(wrong_confidences, bins=20, alpha=0.7, color='orange', edgecolor='black')
        plt.xlabel('Prediction Confidence', fontsize=12)
        plt.ylabel('Error Sample Count', fontsize=12)
        plt.title('Confidence Distribution of Error Predictions', fontsize=14, fontweight='bold')
        plt.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig('error_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()
        print("Error analysis saved as error_analysis.png")

        # Print detailed error analysis report
        print("\n=== Error Analysis Report ===")
        print(f"Total test samples: {len(y_true)}")
        print(f"Incorrectly predicted samples: {len(wrong_indices)}")
        print(f"Error rate: {len(wrong_indices)/len(y_true)*100:.2f}%")
        print(f"False positives: {false_positives} (misdiagnosed as cancer)")
        print(f"False negatives: {false_negatives} (missed cancer)")

        if false_negatives > 0:
            print(f"⚠️  Warning: {false_negatives} cancer samples were misdiagnosed as normal, this is a serious issue in medical diagnosis!")

        if false_positives > 0:
            print(f"ℹ️  Info: {false_positives} normal samples were misdiagnosed as cancer, may cause unnecessary concern.")

def main():
    """Main function"""
    print("🔬 Colon Cancer Cell Classifier Training Program")
    print("="*50)

    # Initialize system
    print("Initializing system...")

    # Check if dataset exists
    data_dir = 'colon_dataset_split'
    if not os.path.exists(data_dir):
        print(f"❌ Dataset directory does not exist: {data_dir}")
        print("Please run data splitting script first")
        return

    # Check training and testing directories
    train_dir = os.path.join(data_dir, 'train')
    test_dir = os.path.join(data_dir, 'test')

    if not os.path.exists(train_dir):
        print(f"❌ Training data directory does not exist: {train_dir}")
        return

    if not os.path.exists(test_dir):
        print(f"❌ Test data directory does not exist: {test_dir}")
        return

    # Dataset statistics
    print(f"\n📊 Dataset Information:")
    for subset in ['train', 'test']:
        subset_dir = os.path.join(data_dir, subset)
        for class_name in ['colon_aca', 'colon_n']:
            class_dir = os.path.join(subset_dir, class_name)
            if os.path.exists(class_dir):
                count = len([f for f in os.listdir(class_dir) if f.lower().endswith(('.png', '.jpg', '.jpeg'))])
                class_label = "Cancer Cells" if class_name == 'colon_aca' else "Normal Cells"
                print(f"  {subset} - {class_label}: {count} images")

    # Create classifier instance
    classifier = ColonCancerClassifier()

    # Check for existing trained models
    model_files = ['best_colon_cancer_model.pth', 'colon_cancer_classifier.pth']
    existing_model = None

    for model_file in model_files:
        if os.path.exists(model_file):
            existing_model = model_file
            break

    if existing_model:
        print(f"\n🔍 Found existing trained model: {existing_model}")
        choice = input("Use existing model for analysis? (y/n, default y): ").strip().lower()

        if choice in ['', 'y', 'yes']:
            print(f"📥 Loading model: {existing_model}")
            if classifier.load_model(existing_model):
                print("✅ Model loaded successfully!")

                # Prepare test data for FINAL analysis (test set used only once)
                print("\n📊 Preparing test data for FINAL evaluation...")
                print("⚠️  This will be the ONLY time the test set is used for evaluation")
                _, test_loader = classifier.prepare_data(data_dir, include_validation=False)

                # Perform comprehensive analysis (FINAL evaluation only)
                print("\n🔬 Starting FINAL model analysis on test set...")
                classifier.comprehensive_analysis(test_loader)

                print("\n✅ FINAL model analysis complete!")
            else:
                print("❌ Model loading failed, will retrain")
                existing_model = None
        else:
            print("🔄 User chose to retrain model")
            existing_model = None

    # If no model or user chose to retrain
    if not existing_model:
        try:
            print(f"\n🚀 Starting new model training...")
            classifier.train(epochs=30)
            print("\n✅ Training and analysis complete!")
        except KeyboardInterrupt:
            print("\n⚠️ Training interrupted by user")
        except Exception as e:
            print(f"\n❌ Error during training: {e}")
            import traceback
            traceback.print_exc()
            return

    # Display generated analysis files
    print("\n📈 Generated Analysis Files:")
    analysis_files = [
        'training_history.png',
        'confusion_matrix.png',
        'roc_curve.png',
        'precision_recall_curve.png',
        'classification_report.png',
        'prediction_distribution.png',
        'shap_analysis.png',
        'feature_importance.png',
        'performance_metrics.png',
        'error_analysis.png'
    ]

    for file in analysis_files:
        if os.path.exists(file):
            print(f"  ✓ {file}")
        else:
            print(f"  ✗ {file} (not generated)")

if __name__ == "__main__":
    main()
